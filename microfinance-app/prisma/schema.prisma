generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgres"
  url      = env("DATABASE_URL")
}

model User {
  id            Int            @id @default(autoincrement())
  name          String
  email         String         @unique
  password      String
  role          String         @default("user")
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  chitFunds     ChitFund[]
  globalMembers GlobalMember[]
  loans         Loan[]
  partners      Partner[]
  repayments    Repayment[]
  transactions  Transaction[]
  contributions Contribution[] @relation("CreatedContributions")
}

model ChitFund {
  id                     Int                   @id @default(autoincrement())
  name                   String
  totalAmount            Float
  monthlyContribution    Float
  firstMonthContribution Float?
  duration               Int
  membersCount           Int
  status                 String                @default("Active")
  startDate              DateTime
  currentMonth           Int                   @default(1)
  nextAuctionDate        DateTime?
  description            String?
  chitFundType           String                @default("Auction")
  createdAt              DateTime              @default(now())
  updatedAt              DateTime              @updatedAt
  createdById            Int
  auctions               Auction[]
  createdBy              User                  @relation(fields: [createdById], references: [id], map: "ChitFund_createdById_fkey")
  fixedAmounts           ChitFundFixedAmount[]
  contributions          Contribution[]
  members                Member[]

  @@index([createdById])
}

model GlobalMember {
  id              Int      @id @default(autoincrement())
  name            String
  contact         String
  email           String?
  address         String?
  notes           String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  createdById     Int
  createdBy       User     @relation(fields: [createdById], references: [id], map: "GlobalMember_createdById_fkey")
  loans           Loan[]   @relation("BorrowerToLoan")
  chitFundMembers Member[]

  @@index([createdById])
}

model Member {
  id             Int            @id @default(autoincrement())
  globalMemberId Int
  chitFundId     Int
  joinDate       DateTime       @default(now())
  auctionWon     Boolean        @default(false)
  auctionMonth   Int?
  contribution   Float
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  auctions       Auction[]
  contributions  Contribution[]
  chitFund       ChitFund       @relation(fields: [chitFundId], references: [id], map: "Member_chitFundId_fkey")
  globalMember   GlobalMember   @relation(fields: [globalMemberId], references: [id], map: "Member_globalMemberId_fkey")

  @@index([chitFundId])
  @@index([globalMemberId])
}

model Contribution {
  id                     Int       @id @default(autoincrement())
  amount                 Float
  month                  Int
  paidDate               DateTime
  memberId               Int
  chitFundId             Int
  balance                Float     @default(0)
  balancePaymentDate     DateTime?
  balancePaymentStatus   String?   @default("Pending")
  actualBalancePaymentDate DateTime?
  notes                  String?
  collected_by_id        Int?
  entered_by_id          Int?
  createdById            Int?
  createdAt              DateTime  @default(now())
  updatedAt              DateTime  @updatedAt

  // --- New Relation ---
  transactionId          Int?        @unique
  transaction            Transaction?  @relation(fields: [transactionId], references: [id])

  chitFund               ChitFund  @relation(fields: [chitFundId], references: [id], map: "Contribution_chitFundId_fkey")
  member                 Member    @relation(fields: [memberId], references: [id], map: "Contribution_memberId_fkey")
  collectedBy            Partner?  @relation("CollectedContributions", fields: [collected_by_id], references: [id])
  enteredBy              Partner?  @relation("EnteredContributions", fields: [entered_by_id], references: [id])
  createdBy              User?     @relation("CreatedContributions", fields: [createdById], references: [id])

  @@index([chitFundId])
  @@index([memberId])
  @@index([collected_by_id])
  @@index([entered_by_id])
  @@index([createdById])
  @@index([transactionId]) // Added index for the new foreign key
}

model Auction {
  id              Int       @id @default(autoincrement())
  chitFundId      Int
  month           Int
  date            DateTime
  winnerId        Int
  amount          Float
  lowestBid       Float?
  highestBid      Float?
  numberOfBidders Int?
  notes           String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // --- New Relations ---
  disbursed_by_id Int?
  entered_by_id   Int?
  transactionId   Int?      @unique

  disbursedBy     Partner?    @relation("DisbursedAuctions", fields: [disbursed_by_id], references: [id])
  enteredBy       Partner?    @relation("EnteredAuctions", fields: [entered_by_id], references: [id])
  transaction     Transaction?@relation(fields: [transactionId], references: [id])
  // --- End New Relations ---

  chitFund        ChitFund  @relation(fields: [chitFundId], references: [id], map: "Auction_chitFundId_fkey")
  winner          Member    @relation(fields: [winnerId], references: [id], map: "Auction_winnerId_fkey")

  @@index([chitFundId])
  @@index([winnerId])
  @@index([disbursed_by_id])
  @@index([entered_by_id])
  @@index([transactionId])
}

model ChitFundFixedAmount {
  id         Int      @id @default(autoincrement())
  chitFundId Int
  month      Int
  amount     Float
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  chitFund   ChitFund @relation(fields: [chitFundId], references: [id], map: "ChitFundFixedAmount_chitFundId_fkey")

  @@unique([chitFundId, month])
  @@index([chitFundId])
}

model Loan {
  id                  Int       @id @default(autoincrement())
  borrowerId          Int
  loanType            String
  amount              Float
  interestRate        Float
  documentCharge      Float     @default(0)
  currentMonth        Int       @default(0)
  installmentAmount   Float     @default(0)
  duration            Int
  disbursementDate    DateTime
  repaymentType       String
  remainingAmount     Float
  overdueAmount       Float     @default(0)
  missedPayments      Int       @default(0)
  nextPaymentDate     DateTime?
  status              String    @default("Active")
  purpose             String?
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt
  createdById         Int

  // --- New Relations ---
  disbursed_by_id     Int?
  entered_by_id       Int?
  transactionId       Int?      @unique

  disbursedBy         Partner?    @relation("DisbursedLoans", fields: [disbursed_by_id], references: [id])
  enteredBy           Partner?    @relation("EnteredLoans", fields: [entered_by_id], references: [id])
  transaction         Transaction?@relation(fields: [transactionId], references: [id])
  // --- End New Relations ---

  borrower            GlobalMember      @relation("BorrowerToLoan", fields: [borrowerId], references: [id], map: "Loan_borrowerId_fkey")
  createdBy           User              @relation(fields: [createdById], references: [id], map: "Loan_createdById_fkey")
  paymentSchedules    PaymentSchedule[]
  repayments          Repayment[]

  @@index([borrowerId])
  @@index([createdById])
  @@index([disbursed_by_id])
  @@index([entered_by_id])
  @@index([transactionId])
}


model Repayment {
  id            Int      @id @default(autoincrement())
  amount        Float
  paidDate      DateTime
  period        Int
  loanId        Int
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  paymentType   String
  collected_by_id Int?
  createdById   Int?
  entered_by_id Int?

  // --- New Relation ---
  transactionId Int?       @unique
  transaction   Transaction? @relation(fields: [transactionId], references: [id])

  collectedBy   Partner? @relation("CollectedRepayments", fields: [collected_by_id], references: [id])
  createdBy     User?    @relation(fields: [createdById], references: [id])
  enteredBy     Partner? @relation("EnteredRepayments", fields: [entered_by_id], references: [id])
  loan          Loan     @relation(fields: [loanId], references: [id], onDelete: Cascade)

  @@index([createdById])
  @@index([loanId])
  @@index([collected_by_id])
  @@index([entered_by_id])
  @@index([paidDate])
  @@index([transactionId]) // Added index for the new foreign key
}

model PaymentSchedule {
  id                Int       @id @default(autoincrement())
  loanId            Int
  period            Int
  dueDate           DateTime
  amount            Float
  status            String    @default("Pending")
  actualPaymentDate DateTime?
  notes             String?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  loan              Loan      @relation(fields: [loanId], references: [id], map: "PaymentSchedule_loanId_fkey")

  @@index([loanId])
}

model EmailLog {
  id           Int      @id @default(autoincrement())
  emailType    String
  period       String
  sentDate     DateTime
  status       String   @default("sent")
  recipients   String
  fileName     String?
  isRecovery   Boolean  @default(false)
  errorMessage String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@unique([emailType, period])
  @@index([emailType, sentDate])
}

model Partner {
  id                       Int            @id @default(autoincrement())
  name                     String
  code                     String?
  isActive                 Boolean        @default(true)
  createdAt                DateTime       @default(now())
  updatedAt                DateTime       @updatedAt
  createdById              Int
  
  createdBy                User           @relation(fields: [createdById], references: [id])
  
  // --- Expanded Relations ---
  collectedRepayments      Repayment[]    @relation("CollectedRepayments")
  enteredRepayments        Repayment[]    @relation("EnteredRepayments")
  collectedContributions   Contribution[] @relation("CollectedContributions")
  enteredContributions     Contribution[] @relation("EnteredContributions")
  disbursedAuctions        Auction[]      @relation("DisbursedAuctions")
  enteredAuctions          Auction[]      @relation("EnteredAuctions")
  disbursedLoans           Loan[]         @relation("DisbursedLoans")
  enteredLoans             Loan[]         @relation("EnteredLoans")
  // --- End Expanded Relations ---

  fromTransactions         Transaction[]  @relation("FromPartnerTransactions")
  toTransactions           Transaction[]  @relation("ToPartnerTransactions")

  @@unique([createdById, code], name: "Partner_createdById_code_key")
  @@index([createdById])
}

model Transaction {
  id                Int       @id @default(autoincrement())
  type              String
  amount            Float
  from_partner      String?
  to_partner        String?
  action_performer  String
  entered_by        String
  date              DateTime
  note              String?
  partnerBalance    Float?    @default(0) // Running balance for the affected partner
  totalBalance      Float?    @default(0) // Running total balance across all partners
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  createdById       Int
  from_partner_id   Int?
  to_partner_id     Int?

  createdBy         User      @relation(fields: [createdById], references: [id])
  fromPartner       Partner?  @relation("FromPartnerTransactions", fields: [from_partner_id], references: [id])
  toPartner         Partner?  @relation("ToPartnerTransactions", fields: [to_partner_id], references: [id])

  // --- Expanded Relations ---
  repayment         Repayment?
  contribution      Contribution?
  auction           Auction?
  loan              Loan?

  @@index([createdById])
  @@index([from_partner_id])
  @@index([to_partner_id])
  @@index([type])
  @@index([date])
}

enum RepaymentType {
  REGULAR
  INTEREST_ONLY
  PARTIAL
}
